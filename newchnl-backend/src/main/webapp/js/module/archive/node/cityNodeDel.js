/*
 * $Id: CityNodeDel.js,v 1.18 2015/05/26 09:42:09 fuqiang Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
define(['common/businessHours', 'common/gridInfoTree'], function (require) {
    function CityNodeDel(config) {
        CityNodeDel.superclass.constructor.call(this, config);
        this.init();
    }

    CityNodeDel.ATTRS = {};
    BUI.extend(CityNodeDel, BUI.Base);
    BUI.augment(CityNodeDel, {
        init: function () {
            // layout
            var myLayout = new dhtmlXLayoutObject({
                parent: document.body,
                pattern: "1C",
                offsets: {
                    top: 10,
                    right: 10,
                    bottom: 10,
                    left: 10
                },
                cells: [{
                    id: "a",
                    header: false
                }]
            });
            // Form
            var formStructure = [{
                type: "settings",
                position: "label-left",
                labelWidth: 120,
                labelAlign: "right"
            }, {
                type: "fieldset",
                label: "基本信息",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{type: "block", name: "row1_0"},
                    {type: "block", name: "row1_1"},
                    {type: "block", name: "row1_1_1"},
                    {type: "block", name: "row1_2_1"},
                    {type: "block", name: "row1_2"},
                    {type: "block", name: "row1_3"},
                    {type: "block", name: "row1_4"},
                    {type: "block", name: "row1_5"},
                    {type: "block", name: "row1_6"},
                    {type: "block", name: "row1_7"},
                    {type: "block", name: "row1_8"},
                    {type: "block", name: "row1_9"},
                    {type: "block", name: "row1_10"},
                    {type: "block", name: "row1_11"},
                    {type: "block", name: "row1_12"},
                    {type: "block", name: "row1_13"},
                    {type: "block", name: "row1_14"},
                    {type: "block", name: "row1_15"},
                    {type: "block", name: "row1_17"},
                    {type: "block", name: "row1_18"},
                    {type: "block", name: "row1_19"}]
            }, {
                type: "fieldset",
                label: "合同信息",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{type: "block", name: "row31_1"},
                    {type: "block", name: "row31_2"},
                    {type: "block", name: "row31_3"},
                    {type: "block", name: "row31_4"}]
            }, {
                type: "fieldset",
                label: "集中地信息",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{type: "block", name: "row2_1"},
                    {type: "block", name: "row2_2"},
                    {type: "block", name: "row2_3"}]
            }, {
                type: "fieldset",
                label: "商圈信息",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{type: "block", name: "row3_1"},
                    {type: "block", name: "row3_2"},
                    {type: "block", name: "row3_3"}]
            }, {
                type: "fieldset",
                label: "门店状态",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{type: "block", name: "row5_1"}]
            }, {
                type: "fieldset",
                label: "授权编码",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{type: "block", name: "row6_1"}]
            }, {
                type: "fieldset",
                label: "网点联系信息",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{type: "block", name: "row8_1"}]
            }, {
                type: "fieldset",
                label: "取号信息",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{
                    type: "block",
                    name: "row24"
                }, {
                    type: "block",
                    name: "row24_1"
                }]
            }, {
                type: "fieldset",
                label: "总部维护信息",
                offsetLeft: 20,
                offsetTop: 20,
                list: [{
                    type: "block",
                    name: "row41_1"
                }, {
                    type: "block",
                    name: "row41_2"
                }, {
                    type: "block",
                    name: "row41_3"
                }, {
                    type: "block",
                    name: "row41_4"
                }, {
                    type: "block",
                    name: "row41_5"
                }, {
                    type: "block",
                    name: "row41_6"
                }, {
                    type: "block",
                    name: "row41_7"
                }, {
                    type: "block",
                    name: "row41_8"
                },{
                    type: "block",
                    name: "row43"
                },{
                    type: "block",
                    name: "row42_1"
                },{
                    type: "block",
                    name: "row42_1_1"
                },{
                    type: "block",
                    name: "row42_2"
                },{
                    type: "block",
                    name: "row42_2_1"
                },{
                    type: "block",
                    name: "row42_3"
                },{
                    type: "block",
                    name: "row42_3_1"
                },{
                    type: "block",
                    name: "row42_3_2"
                },{
                    type: "block",
                    name: "row42_4"
                },{
                    type: "block",
                    name: "row42_4_1"
                },{
                    type: "block",
                    name: "row42_5"
                },{
                    type: "block",
                    name: "row42_5_1"
                },{
                    type: "block",
                    name: "row42_6"
                },{
                    type: "block",
                    name: "row42_6_1"
                }]
            }, {
                type: "block",
                name: "row10_1"
            }];

            var myForm = myLayout.cells("a").attachForm(formStructure);
            myForm.enableLiveValidation(true);

            // 设置界面
            myForm.addItem("row1_0", _channelEntityId, 0);
            myForm.addItem("row1_0", _newcolumn, 1);
            myForm.addItem("row1_0", _unifyCode, 2);

            myForm.addItem("row1_1", _channelClassFirst_1, 0);  //一级渠道分类
            myForm.addItem("row1_1", _newcolumn, 1);
            myForm.addItem("row1_1", _channelClassSecond_1, 2); //二级渠道分类
            myForm.addItem("row1_1", _newcolumn, 3);
            myForm.addItem("row1_1", _channelClassThird_1, 4);  //三级渠道分类

            myForm.addItem("row1_1_1", _nodeKind_1, 0);
            myForm.addItem("row1_1_1",_newcolumn, 1);
            myForm.addItem("row1_1_1",_districtId, 2);			//归属组织
            myForm.addItem("row1_1_1",_newcolumn, 3);
            myForm.addItem("row1_1_1",_regionId, 4); 			//行政区

            myForm.addItem("row1_2_1", _isEnterGrid, 0);
            myForm.addItem("row1_2_1", _newcolumn, 1);
            myForm.addItem("row1_2_1", _gridTreeNullable, 2);	//归属网格
            myForm.addItem("row1_2_1", _newcolumn, 3);
            myForm.addItem("row1_2_1", _gridName, 4);
            myForm.addItem("row1_2_1", _newcolumn, 5);
            myForm.addItem("row1_2_1", _streetId, 6);


            myForm.addItem("row1_2", _parentEntityName_1, 0);	//代理商名称
            myForm.addItem("row1_2", _parentEntity, 1); 		//代理商ID 隐藏在此
            myForm.addItem("row1_2", _newcolumn, 4);
            myForm.addItem("row1_2", _areaShape, 5);			//区域形态

            myForm.addItem("row1_3", _channelEntityName_1, 0);//网点名称
            myForm.addItem("row1_3", _newcolumn, 1);
            myForm.addItem("row1_3", _nodeAddr_1, 2);			//网点地址
            myForm.addItem("row1_3", _newcolumn, 3);
            myForm.addItem("row1_3", _newcolumn, 4);

            myForm.addItem("row1_4", _nodeType, 0);			//渠道类型
            myForm.addItem("row1_4", _newcolumn, 1);
            myForm.addItem("row1_4", _isNetwork, 2);			//是否联网
            myForm.addItem("row1_4", _newcolumn, 3);
            myForm.addItem("row1_4", _isExclusive, 4);		//是否排他

            myForm.addItem("row1_5", _systemConnection, 0); //系统接入
            myForm.addItem("row1_5", _newcolumn, 1);
            myForm.addItem("row1_5", _A, 2);
            myForm.addItem("row1_5", _newcolumn, 3);
            myForm.addItem("row1_5", _B, 4);
            myForm.addItem("row1_5", _newcolumn, 5);
            myForm.addItem("row1_5", _C, 6);
            myForm.addItem("row1_5", _newcolumn, 7);
            myForm.addItem("row1_5", _D, 8);
            myForm.addItem("row1_5", _newcolumn, 9);
            myForm.addItem("row1_5", _E, 10);
            myForm.addItem("row1_5", _systemConnectionType, 11); //系统接入类型

            myForm.addItem("row1_6", _simSaleMonthly, 0);		//月号码销量
            myForm.addItem("row1_6", _newcolumn, 1);
            myForm.addItem("row1_6", _cellSaleMonthly, 2);    //月手机销量
            myForm.addItem("row1_6", _newcolumn, 3);
            myForm.addItem("row1_6", _g3g4CellSaleMonthly, 4);//月3G/4G手机销

            myForm.addItem("row1_7", _buildingAmount, 0);		//设备投资总额
            myForm.addItem("row1_7", _newcolumn, 1);
            myForm.addItem("row1_7", _eqmtAmount, 2);    		//装修投资总额
            myForm.addItem("row1_7", _newcolumn, 3);
            myForm.addItem("row1_7", _officesAmount, 4);		//办公和营业家具累计投资总额

            myForm.addItem("row1_8", _accreditExt4, 0);		//是否授权网点
            myForm.addItem("row1_8", _newcolumn, 1);
            myForm.addItem("row1_8", _isAuthorized, 2);		//是否异业授权

            myForm.addItem("row1_9", _nodeLevel, 0);			//网点星级
            myForm.addItem("row1_9", _newcolumn, 1);
            myForm.addItem("row1_9", _signBeginDate, 2);    	//签约时间
            myForm.addItem("row1_9", _newcolumn, 3);
            myForm.addItem("row1_9", _signEndDate, 4);		//协议截止时间

            myForm.addItem("row1_10", _useArea, 0);			//营业面积
            myForm.addItem("row1_10", _newcolumn, 1);
            myForm.addItem("row1_10", _staffNum, 2);    		//营业员数量
            myForm.addItem("row1_10", _newcolumn, 3);
            myForm.addItem("row1_10", _newcolumn, 4);

            myForm.addItem("row1_11", _businessStartDate, 0);	//开业时间
            myForm.addItem("row1_11", _newcolumn, 1);
            myForm.addItem("row1_11", _businessTime, 2);    		//营业时间
            myForm.addItem("row1_11", _newcolumn, 3);
            myForm.addItem("row1_11", _newcolumn, 4);

            myForm.addItem("row1_12", _postCode, 0);			//门店邮编
            myForm.addItem("row1_12", _newcolumn, 1);
            myForm.addItem("row1_12", _unityLicenseName, 2);   //个体工商户名称
            myForm.addItem("row1_12", _newcolumn, 3);
            myForm.addItem("row1_12", _nodeLicenseId, 4);		//门店工商号

            myForm.addItem("row1_13", _longitude, 0);			//经度
            myForm.addItem("row1_13", _newcolumn, 1);
            myForm.addItem("row1_13", _latitude, 2);    		//纬度
            myForm.addItem("row1_13", _newcolumn, 3);
            myForm.addItem("row1_13", _channelEntitySerial, 4); //序列号

            myForm.addItem("row1_14", _isRLXS, 0);
            myForm.addItem("row1_14", _newcolumn, 1);
            myForm.addItem("row1_14", _is5G, 2);


            myForm.addItem("row1_19", _trustees, 2);    //托管方
            myForm.addItem("row1_19", _newcolumn, 1);

            //取号信息
            myForm.addItem("row24", _selectDockingSystem, 0);
            myForm.addItem("row24", _newcolumn, 1);
            myForm.addItem("row24", _isZxqh, 2);
            myForm.addItem("row24", _newcolumn, 3);
            myForm.addItem("row24", _isYyqh, 4);
            myForm.addItem("row24_1", _zxqhSetting, 0);
            myForm.addItem("row24_1", _newcolumn, 1);
            myForm.addItem("row24_1", _yyqhSetting, 2);


            myForm.addItem("row31_1", _contractNumber1, 0);   			//合同信息   合同编号 合同标的 合同金额
            myForm.addItem("row31_1", _newcolumn, 1);
            myForm.addItem("row31_1", _contractMarkTarget, 2);
            myForm.addItem("row31_1", _newcolumn, 3);
            myForm.addItem("row31_1", _contractBeginDate, 4);
            myForm.addItem("row31_2", _newcolumn, 5);//合同开始  合同结束  递延月份
            myForm.addItem("row31_2", _contractEndDate, 6);
            myForm.addItem("row31_2", _newcolumn, 7);
            myForm.addItem("row31_2", _passOldMonth, 8);
            myForm.addItem("row31_2", _newcolumn, 9);
            myForm.addItem("row31_2", _amountIncludingTax, 10);
            myForm.addItem("row31_2", _newcolumn, 11);
            myForm.addItem("row31_3", _amountExcludingTax, 12);
            myForm.addItem("row31_3", _newcolumn, 13);
            myForm.addItem("row31_3", _vendorCode, 14);
            myForm.addItem("row31_3", _newcolumn, 15);
            myForm.addItem("row31_3", _vendorNameC, 16);
            myForm.addItem("row31_4", _constractNewsConnection, 0);

            myForm.addItem("row2_1", _isCentral, 0);			//是否在集中地
            myForm.addItem("row2_1", _newcolumn, 1);
            myForm.addItem("row2_1", _newcolumn, 2);    	//选择集中地按钮
            myForm.addItem("row2_1", _newcolumn, 3);
            myForm.addItem("row2_1", _centralId, 4);			//集中地编号     隐藏在此

            myForm.addItem("row2_2", _centralName, 0);		//集中地名称
            myForm.addItem("row2_2", _newcolumn, 1);
            myForm.addItem("row2_2", _newcolumn, 2);
            myForm.addItem("row2_2", _newcolumn, 3);
            myForm.addItem("row2_2", _newcolumn, 4);

            myForm.addItem("row2_3", _centralPosition, 0);	//集中地位置
            myForm.addItem("row2_3", _newcolumn, 1);
            myForm.addItem("row2_3", _newcolumn, 2);
            myForm.addItem("row2_3", _newcolumn, 3);
            myForm.addItem("row2_3", _newcolumn, 4);

            myForm.addItem("row3_1", _isCircle, 0);			//是否在商圈
            myForm.addItem("row3_1", _newcolumn, 1);
            myForm.addItem("row3_1", _newcolumn, 2);    	//选择商圈按钮
            myForm.addItem("row3_1", _newcolumn, 3);
            myForm.addItem("row3_1", _newcolumn, 4);

            myForm.addItem("row3_2", _circleLevel, 0);		//商圈级别
            myForm.addItem("row3_2", _newcolumn, 1);
            myForm.addItem("row3_2", _circleName, 2);    		//商圈名称
            myForm.addItem("row3_2", _newcolumn, 3);
            myForm.addItem("row3_2", _circleId, 4);		    //商圈名称ID  隐藏在此

            myForm.addItem("row3_3", _circlePlace, 0);		//商圈内位置
            myForm.addItem("row3_3", _newcolumn, 1);
            myForm.addItem("row3_3", _newcolumn, 2);
            myForm.addItem("row3_3", _newcolumn, 3);
            myForm.addItem("row3_3", _newcolumn, 4);

            myForm.addItem("row5_1", _channelEntityStatus, 0);//门店状态
            myForm.addItem("row5_1", _newcolumn, 1);
            myForm.addItem("row5_1", _pauseReason, 2);    	//暂停原因

            myForm.addItem("row6_1", _nodeAuthoriztionId, 0); //授权编码
            myForm.addItem("row6_1", _newcolumn, 1);
            myForm.addItem("row6_1", _newcolumn, 2);
            myForm.addItem("row6_1", _newcolumn, 3);
            myForm.addItem("row6_1", _newcolumn, 4);

            myForm.addItem("row8_1", _rel_relationName_1, 0); //负责人名称
            myForm.addItem("row8_1", _newcolumn, 1);
            myForm.addItem("row8_1", _rel_relationMobile_1, 2);//负责人手机
            myForm.addItem("row8_1", _newcolumn, 3);
            myForm.addItem("row8_1", _rel_email_1, 4); 		//负责人邮箱
            myForm.addItem("row8_1", _rel_relationType_1, 5); //负责人类型       隐藏在此

            myForm.addItem("row41_3", _isChannelNationalSubsidy, 0);
            myForm.addItem("row41_3", _newcolumn, 1);

            myForm.addItem("row41_5", _addressType, 0);
            //myForm.addItem("row41_5", _newcolumn, 1);
            //myForm.addItem("row41_5", _propertyNature, 2);		//房产性质
            myForm.addItem("row41_5", _newcolumn, 3);
            myForm.addItem("row41_5", _sumArea, 4);			//总面积
            myForm.addItem("row41_5", _newcolumn, 5);
            myForm.addItem("row41_5", _cooperateEndDate, 6); //合作协议到期时间

            // myForm.addItem("row41_6", _managementStyle, 0);
            // myForm.addItem("row41_6", _newcolumn, 1);
            // myForm.addItem("row41_6", _cooperationType, 2);
            // myForm.addItem("row41_6", _newcolumn, 3);
            // myForm.addItem("row41_6", _businessPatterns, 4);

            // myForm.addItem("row41_7", _industryPatterns, 0);
            // myForm.addItem("row41_7", _newcolumn, 1);
            // myForm.addItem("row41_7", _industryPatternsAttrs, 2);
            myForm.addItem("row41_7", _newcolumn, 3);
            myForm.addItem("row41_7", _geographicalPositionType, 4);
            // myForm.addItem("row41_7", _newcolumn, 5);
            // myForm.addItem("row41_7", _subDivision, 6);

            // myForm.addItem("row42", _panChannelType, 0);
            // myForm.addItem("row42", _newcolumn, 1);
            // myForm.addItem("row42", _panChannelExpansionMode, 2);

            myForm.addItem("row43", _manageScopeLabel, 0);
            myForm.addItem("row43", _manageScope, 1);
            myForm.addItem("row42_1", _manageScopeLabel1, 0);    //营业厅业务范围  查询服务
            myForm.addItem("row42_2", _manageScopeLabel2, 0);   //营业厅业务范围   充值服务
            myForm.addItem("row42_3", _manageScopeLabel3, 0);   //营业厅业务范围   服务办理
            myForm.addItem("row42_4", _manageScopeLabel4, 0);   //营业厅业务范围   业务办理
            myForm.addItem("row42_5", _manageScopeLabel5, 0);   //营业厅业务范围   实物销售
            myForm.addItem("row42_6", _manageScopeLabel6, 0);   //营业厅业务范围   宣传类

            myForm.addItem("row42_1_1", _manageScope1, 1);
            myForm.addItem("row42_1_1", _newcolumn, 2);
            myForm.addItem("row42_1_1", _manageScope2, 3);
            myForm.addItem("row42_1_1", _newcolumn, 4);
            myForm.addItem("row42_1_1", _manageScope3, 5);
            myForm.addItem("row42_1_1", _newcolumn, 6);
            myForm.addItem("row42_1_1", _manageScope4, 7);

            // myForm.addItem("row42_2_1", _manageScope5, 1);
            // myForm.addItem("row42_2_1", _newcolumn, 2);
            // myForm.addItem("row42_2_1", _manageScope6, 3);

            myForm.addItem("row42_3_1", _manageScope7, 1);
            myForm.addItem("row42_3_1", _newcolumn, 2);
            myForm.addItem("row42_3_1", _manageScope8, 3);
            myForm.addItem("row42_3_1", _newcolumn, 4);
            myForm.addItem("row42_3_1", _manageScope9, 5);
            myForm.addItem("row42_3_1", _newcolumn, 6);
            myForm.addItem("row42_3_1", _manageScope10, 7);
            myForm.addItem("row42_3_1", _newcolumn, 8);
            myForm.addItem("row42_3_1", _manageScope11, 9);
            myForm.addItem("row42_3_1", _newcolumn, 10);
            myForm.addItem("row42_3_1", _manageScope12, 11);
            myForm.addItem("row42_3_1", _newcolumn, 12);
            myForm.addItem("row42_3_1", _manageScope13, 13);
            myForm.addItem("row42_3_1", _newcolumn, 14);
            myForm.addItem("row42_3_1", _manageScope14, 15);
            myForm.addItem("row42_3_1", _newcolumn, 16);
            myForm.addItem("row42_3_1", _manageScope15, 17);
            myForm.addItem("row42_3_1", _newcolumn, 18);
            myForm.addItem("row42_3_1", _manageScope16, 19);
            myForm.addItem("row42_3_1", _newcolumn, 20);
            myForm.addItem("row42_3_1", _manageScope17, 21);
            myForm.addItem("row42_3_1", _newcolumn, 22);
            myForm.addItem("row42_3_1", _manageScope18, 23);

            myForm.addItem("row42_3_2", _manageScope19, 1);
            myForm.addItem("row42_3_2", _newcolumn, 2);
            myForm.addItem("row42_3_2", _manageScope20, 3);
            myForm.addItem("row42_3_2", _newcolumn, 4);
            myForm.addItem("row42_3_2", _manageScope21, 5);
            myForm.addItem("row42_3_2", _newcolumn, 6);
            myForm.addItem("row42_3_2", _manageScope22, 7);
            myForm.addItem("row42_3_2", _newcolumn, 8);
            myForm.addItem("row42_3_2", _manageScope23, 9);
            myForm.addItem("row42_3_2", _newcolumn, 10);
            myForm.addItem("row42_3_2", _manageScope24, 11);
            myForm.addItem("row42_3_2", _newcolumn, 12);
            myForm.addItem("row42_3_2", _manageScope25, 13);
            myForm.addItem("row42_3_2", _newcolumn, 14);
            myForm.addItem("row42_3_2", _manageScope26, 15);
            myForm.addItem("row42_3_2", _newcolumn, 16);
            myForm.addItem("row42_3_2", _manageScope27, 17);
            myForm.addItem("row42_3_2", _newcolumn, 18);
            myForm.addItem("row42_3_2", _manageScope28, 19);
            myForm.addItem("row42_3_2", _newcolumn, 20);
            myForm.addItem("row42_3_2", _manageScope29, 21);
            myForm.addItem("row42_3_2", _newcolumn, 22);
            myForm.addItem("row42_3_2", _manageScope30, 23);

            myForm.addItem("row42_4_1", _manageScope31, 1);
            myForm.addItem("row42_4_1", _newcolumn, 2);
            myForm.addItem("row42_4_1", _manageScope32, 3);
            myForm.addItem("row42_4_1", _newcolumn, 4);
            myForm.addItem("row42_4_1", _manageScope33, 5);
            myForm.addItem("row42_4_1", _newcolumn, 6);
            myForm.addItem("row42_4_1", _manageScope34, 7);
            myForm.addItem("row42_4_1", _newcolumn, 8);
            myForm.addItem("row42_4_1", _manageScope35, 9);
            myForm.addItem("row42_4_1", _newcolumn, 10);
            myForm.addItem("row42_4_1", _manageScope36, 11);
            myForm.addItem("row42_4_1", _newcolumn, 12);
            myForm.addItem("row42_4_1", _manageScope37, 13);

            myForm.addItem("row42_5_1", _manageScope38, 1);
            myForm.addItem("row42_5_1", _newcolumn, 2);
            myForm.addItem("row42_5_1", _manageScope39, 3);
            myForm.addItem("row42_5_1", _newcolumn, 4);
            myForm.addItem("row42_5_1", _manageScope40, 5);
            myForm.addItem("row42_5_1", _newcolumn, 6);
            myForm.addItem("row42_5_1", _manageScope41, 7);
            myForm.addItem("row42_5_1", _newcolumn, 8);
            myForm.addItem("row42_5_1", _manageScope42, 9);
            myForm.addItem("row42_5_1", _newcolumn, 10);
            myForm.addItem("row42_5_1", _manageScope43, 11);
            myForm.addItem("row42_5_1", _newcolumn, 12);
            myForm.addItem("row42_5_1", _manageScope44, 13);
            myForm.addItem("row42_5_1", _newcolumn, 14);
            myForm.addItem("row42_5_1", _manageScope45, 15);

            myForm.addItem("row42_6_1", _manageScope46, 1);
            myForm.addItem("row42_6_1", _newcolumn, 2);
            myForm.addItem("row42_6_1", _manageScope47, 3);

            myForm.addItem("row10_1", _closeBtn, 0);

            // 初始化Combo
            myForm.getCombo("nodeKind").addOption(BUI.getComboData(10033, 0));//网点性质
            myForm.getCombo("channelClassFirst").addOption(BUI.getComboData(10056, 0));		//一级渠道分类
            myForm.getCombo("channelClassSecond").addOption(BUI.getComboData(10057, 0));	//二级渠道分类
            myForm.getCombo("channelClassThird").addOption(BUI.getComboData(10058, 0));		//三级渠道分类

            myForm.getCombo("channelEntityStatus").addOption(BUI.getComboData(10003, 0));//门店状态
            myForm.getCombo("districtId").addOption(BUI.getComboData(10002, 0));
            myForm.getCombo("regionId").addOption(BUI.getComboData(10023, 0));
            myForm.getCombo("systemConnection").addOption(BUI.getComboData(50011, 0));//是否联网

            myForm.getCombo("accreditExt4").addOption(BUI.getComboData(50004, 0));//是否授权网点
            myForm.getCombo("isAuthorized").addOption(BUI.getComboData(50084, 0));//是否异业授权
            myForm.getCombo("trustees").addOption(BUI.getComboData(60001));//托管方
            myForm.getCombo("nodeKind").addOption(BUI.getComboData(10033, 0));//渠道性质
            myForm.getCombo("nodeType").addOption(BUI.getComboData(50007, 0));//渠道类型
            myForm.getCombo("simSaleMonthly").addOption(BUI.getComboData(50014, 0));//月号码销量
            myForm.getCombo("cellSaleMonthly").addOption(BUI.getComboData(50015, 0));//月手机销量
            myForm.getCombo("g3g4CellSaleMonthly").addOption(BUI.getComboData(50016, 0));//月3G/4G手机销量
            myForm.getCombo("addressType").addOption(BUI.getComboData(82836, 0));//地理位置类型
            myForm.getCombo("areaShape").addOption(BUI.getComboData(82825, 0));//区域形态
            myForm.getCombo("isExclusive").addOption(BUI.getComboData(10017, 0));//是否排他
            myForm.getCombo("nodeLevel").addOption(BUI.getComboData(10008, 0));//网点星级
            myForm.getCombo("isNetwork").addOption(BUI.getComboData(10018, 0));//是否联网
            myForm.getCombo("isCentral").addOption(BUI.getComboData(50018, 0));//是否在集中地
            myForm.getCombo("isEnterGrid").addOption(BUI.getComboData(50029, 0));//是否入网格
            myForm.getCombo("isCircle").addOption(BUI.getComboData(50004, 0));//是否在圈内
            myForm.getCombo("circleLevel").addOption(BUI.getComboData(50002, 0));//商圈级别
            myForm.getCombo("circlePlace").addOption(BUI.getComboData(50005, 0));//商圈内位置
//获取取号信息中combo数据
            myForm.getCombo("selectDockingSystem").addOption(BUI.getComboData(50186, 0));//选择对接系统
            myForm.getCombo("isZxqh").addOption(BUI.getComboData(50184, 0));//是否开放在线取号
            myForm.getCombo("isYyqh").addOption(BUI.getComboData(50185, 0));//是否开放预约取号

            myForm.getCombo("is5G").addOption(BUI.getComboData(50004, 0));//是否5G
            myForm.getCombo("isRLXS").addOption(BUI.getComboData(50004, 0));//是否让利销售
            // myForm.getCombo("industryPatterns").addOption(BUI.getComboData(50135, 0));//行业形态
            myForm.getCombo("geographicalPositionType").addOption(BUI.getComboData(50130,0));//地理位置
            myForm.getCombo("isChannelNationalSubsidy").addOption(BUI.getComboData(50004, 0));//是否是国补入围渠道
            // myForm.getCombo("subDivision").addOption(BUI.getComboData(50131, 0));//渠道细分形态
            // myForm.getCombo("managementStyle").addOption(BUI.getComboData(50132, 0));//管理方式
            // myForm.getCombo("cooperationType").addOption(BUI.getComboData(50133, 0));//合作形式
            // myForm.getCombo("businessPatterns").addOption(BUI.getComboData(50134, 0));//业务形态
            // myForm.getCombo("panChannelType").addOption(BUI.getComboData(50139, 0));//泛渠道类型
            // myForm.getCombo("panChannelExpansionMode").addOption(BUI.getComboData(50140, 0));//泛渠道拓展方式
            this.set("myForm", myForm);

            _this = this;

            // 加载form表单数据
            var url = BUI.ctx + "/node/queryInfoNew";
            $.post(url, {nodeId: nodeId}, function (result) {
                var data = result.data;
                for (var key in data) {
                    if (data[key] == null) {
                        delete data[key];
                    }
                }
                myForm.load({data: data});
                var gridName = myForm.getItemValue("gridName");
                var streetId = myForm.getItemValue("streetId");
                myForm.setItemValue("streetId", streetId);
                myForm.setItemValue("gridInfo", streetId);
                $("input[name='gridInfo_text']").val(gridName);
                _this.systemConnectionStatus(data.systemConnectionType);
                // _this.industryPatternsAttr(data.industryPatterns, data.industryPatternsAttrs);
                _this.manageScopeRecover(data.manageScope,data.manageScopes);
            }, "json");

            // 禁用所有Item
            myForm.forEachItem(function (name) {
                if (name != 'row10_1' && name != 'closeBtn') {
                    myForm.disableItem(name);
                }
            });


            this.initDomEvent();
        },
        initDomEvent: function () {
            var _this = this;
            var myForm = _this.get("myForm");
            myForm.attachEvent("onButtonClick", function (name) {
                if (name == "closeBtn") {
                    parent.mainPage.closePage();
                }
            });
        },
        systemConnectionStatus: function (type) {
            var myForm = this.get("myForm");
            if (type != null) {
                //type =  $.parseJSON(type);
                type = eval("(" + type + ")");
                if (type.A) {
                    myForm.checkItem("A");
                }
                if (type.B) {
                    myForm.checkItem("B");
                }
                if (type.C) {
                    myForm.checkItem("C");
                }
                if (type.D) {
                    myForm.checkItem("D");
                }
                if (type.E) {
                    myForm.checkItem("E");
                }
            }
        },
        /*industryPatternsAttr:function (codeId,industryPatternsAttrs) {
            var myForm = this.get("myForm");
            if (codeId == 1) {
                myForm.addItem("row41_8", _industryPatternsAttrsA1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsA2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsA3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsA4, 6);
            } else if (codeId == 2) {
                myForm.addItem("row41_8", _industryPatternsAttrsB1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsB2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsB3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsB4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsB5, 8);
            } else if (codeId == 3) {
                myForm.addItem("row41_8", _industryPatternsAttrsC1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsC2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsC3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsC4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsC5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsC6, 10);
                myForm.addItem("row41_8", _newcolumn, 11);
                myForm.addItem("row41_8", _industryPatternsAttrsC7, 12);
                myForm.addItem("row41_8", _newcolumn, 13);
                myForm.addItem("row41_8", _industryPatternsAttrsC8, 14);
            } else if (codeId == 4) {
                myForm.addItem("row41_8", _industryPatternsAttrsD1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsD2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsD3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsD4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsD5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsD6, 10);
                myForm.addItem("row41_8", _newcolumn, 11);
                myForm.addItem("row41_8", _industryPatternsAttrsD7, 12);
                myForm.addItem("row41_8", _newcolumn, 13);
                myForm.addItem("row41_8", _industryPatternsAttrsD8, 14);
                myForm.addItem("row41_8", _newcolumn, 15);
                myForm.addItem("row41_8", _industryPatternsAttrsD9, 16);
                myForm.addItem("row41_8", _newcolumn, 17);
                myForm.addItem("row41_8", _industryPatternsAttrsD10, 18);
                myForm.addItem("row41_8", _newcolumn, 19);
                myForm.addItem("row41_8", _industryPatternsAttrsD11, 20);

            } else if (codeId == 5) {
                myForm.addItem("row41_8", _industryPatternsAttrsE1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsE2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsE3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsE4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsE5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsE6, 10);
                myForm.addItem("row41_8", _newcolumn, 11);
                myForm.addItem("row41_8", _industryPatternsAttrsE7, 12);
                myForm.addItem("row41_8", _newcolumn, 13);
                myForm.addItem("row41_8", _industryPatternsAttrsE8, 14);
                myForm.addItem("row41_8", _newcolumn, 15);
                myForm.addItem("row41_8", _industryPatternsAttrsE9, 16);
                myForm.addItem("row41_8", _newcolumn, 17);
                myForm.addItem("row41_8", _industryPatternsAttrsE10, 18);
                myForm.addItem("row41_8", _newcolumn, 19);
                myForm.addItem("row41_8", _industryPatternsAttrsE11, 20);
            } else if (codeId == 6) {
                myForm.addItem("row41_8", _industryPatternsAttrsF1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsF2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsF3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsF4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsF5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsF6, 10);
                myForm.addItem("row41_8", _newcolumn, 11);
                myForm.addItem("row41_8", _industryPatternsAttrsF7, 12);
            } else if (codeId == 7) {
                myForm.addItem("row41_8", _industryPatternsAttrsG1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsG2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsG3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsG4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsG5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsG6, 10);
                myForm.addItem("row41_8", _newcolumn, 11);
                myForm.addItem("row41_8", _industryPatternsAttrsG7, 12);
                myForm.addItem("row41_8", _newcolumn, 13);
                myForm.addItem("row41_8", _industryPatternsAttrsG8, 14);
                myForm.addItem("row41_8", _newcolumn, 15);
                myForm.addItem("row41_8", _industryPatternsAttrsG9, 16);
                myForm.addItem("row41_8", _newcolumn, 17);
                myForm.addItem("row41_8", _industryPatternsAttrsG10, 18);
                myForm.addItem("row41_8", _newcolumn, 19);
                myForm.addItem("row41_8", _industryPatternsAttrsG11, 20);
                myForm.addItem("row41_8", _newcolumn, 21);
                myForm.addItem("row41_8", _industryPatternsAttrsG12, 22);
                myForm.addItem("row41_8", _newcolumn, 23);
                myForm.addItem("row41_8", _industryPatternsAttrsG13, 24);
                myForm.addItem("row41_8", _newcolumn, 25);
                myForm.addItem("row41_8", _industryPatternsAttrsG14, 26);
                myForm.addItem("row41_8", _newcolumn, 27);
                myForm.addItem("row41_8", _industryPatternsAttrsG15, 28);
                myForm.addItem("row41_8", _newcolumn, 29);
                myForm.addItem("row41_8", _industryPatternsAttrsG16, 30);
                myForm.addItem("row41_8", _newcolumn, 31);
                myForm.addItem("row41_8", _industryPatternsAttrsG17, 32);
            } else if (codeId == 8) {
                myForm.addItem("row41_8", _industryPatternsAttrsH1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsH2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsH3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsH4, 6);
            } else if (codeId == 9) {
                myForm.addItem("row41_8", _industryPatternsAttrsI1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsI2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsI3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsI4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsI5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsI6, 10);
            } else if (codeId == 10) {
                myForm.addItem("row41_8", _industryPatternsAttrsJ1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsJ2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsJ3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsJ4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsJ5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsJ6, 10);
                myForm.addItem("row41_8", _newcolumn, 11);
                myForm.addItem("row41_8", _industryPatternsAttrsJ7, 12);
            } else if (codeId == 11) {
                myForm.addItem("row41_8", _industryPatternsAttrsK1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsK2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsK3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsK4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsK5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsK6, 10);
            } else if (codeId == 12) {
                myForm.addItem("row41_8", _industryPatternsAttrsL1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                myForm.addItem("row41_8", _industryPatternsAttrsL2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsL3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsL4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsL5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsL6, 10);
                myForm.addItem("row41_8", _newcolumn, 11);
                myForm.addItem("row41_8", _industryPatternsAttrsL7, 12);
                myForm.addItem("row41_8", _newcolumn, 13);
                myForm.addItem("row41_8", _industryPatternsAttrsL8, 14);
                myForm.addItem("row41_8", _newcolumn, 15);
                myForm.addItem("row41_8", _industryPatternsAttrsL9, 16);
                myForm.addItem("row41_8", _newcolumn, 17);
                myForm.addItem("row41_8", _industryPatternsAttrsL10, 18);
            }else if (codeId == 99) {
                myForm.addItem("row41_8", _industryPatternsAttrsM1, 0);
                myForm.addItem("row41_8", _newcolumn, 1);
                // myForm.addItem("row41_8", _industryPatternsAttrsM2, 2);
                myForm.addItem("row41_8", _newcolumn, 3);
                myForm.addItem("row41_8", _industryPatternsAttrsM3, 4);
                myForm.addItem("row41_8", _newcolumn, 5);
                myForm.addItem("row41_8", _industryPatternsAttrsM4, 6);
                myForm.addItem("row41_8", _newcolumn, 7);
                myForm.addItem("row41_8", _industryPatternsAttrsM5, 8);
                myForm.addItem("row41_8", _newcolumn, 9);
                myForm.addItem("row41_8", _industryPatternsAttrsM6, 10);
            }
            if (industryPatternsAttrs != null) {
                industryPatternsAttrs = eval("(" + industryPatternsAttrs + ")");
                if (industryPatternsAttrs.A1) {
                    myForm.checkItem("industryPatternsAttrsA1");
                }
                if (industryPatternsAttrs.A2) {
                    myForm.checkItem("industryPatternsAttrsA2");
                }
                if (industryPatternsAttrs.A3) {
                    myForm.checkItem("industryPatternsAttrsA3");
                }
                if (industryPatternsAttrs.A4) {
                    myForm.checkItem("industryPatternsAttrsA4");
                }
                if (industryPatternsAttrs.B1) {
                    myForm.checkItem("industryPatternsAttrsB1");
                }
                if (industryPatternsAttrs.B2) {
                    myForm.checkItem("industryPatternsAttrsB2");
                }
                if (industryPatternsAttrs.B3) {
                    myForm.checkItem("industryPatternsAttrsB3");
                }
                if (industryPatternsAttrs.B4) {
                    myForm.checkItem("industryPatternsAttrsB4");
                }
                if (industryPatternsAttrs.B5) {
                    myForm.checkItem("industryPatternsAttrsB5");
                }
                if (industryPatternsAttrs.C1) {
                    myForm.checkItem("industryPatternsAttrsC1");
                }
                if (industryPatternsAttrs.C2) {
                    myForm.checkItem("industryPatternsAttrsC2");
                }
                if (industryPatternsAttrs.C3) {
                    myForm.checkItem("industryPatternsAttrsC3");
                }
                if (industryPatternsAttrs.C4) {
                    myForm.checkItem("industryPatternsAttrsC4");
                }
                if (industryPatternsAttrs.C5) {
                    myForm.checkItem("industryPatternsAttrsC5");
                }
                if (industryPatternsAttrs.C6) {
                    myForm.checkItem("industryPatternsAttrsC6");
                }
                if (industryPatternsAttrs.C7) {
                    myForm.checkItem("industryPatternsAttrsC7");
                }
                if (industryPatternsAttrs.C8) {
                    myForm.checkItem("industryPatternsAttrsC8");
                }
                if (industryPatternsAttrs.D1) {
                    myForm.checkItem("industryPatternsAttrsD1");
                }
                if (industryPatternsAttrs.D2) {
                    myForm.checkItem("industryPatternsAttrsD2");
                }
                if (industryPatternsAttrs.D3) {
                    myForm.checkItem("industryPatternsAttrsD3");
                }
                if (industryPatternsAttrs.D4) {
                    myForm.checkItem("industryPatternsAttrsD4");
                }
                if (industryPatternsAttrs.D5) {
                    myForm.checkItem("industryPatternsAttrsD5");
                }
                if (industryPatternsAttrs.D6) {
                    myForm.checkItem("industryPatternsAttrsD6");
                }
                if (industryPatternsAttrs.D7) {
                    myForm.checkItem("industryPatternsAttrsD7");
                }
                if (industryPatternsAttrs.D8) {
                    myForm.checkItem("industryPatternsAttrsD8");
                }
                if (industryPatternsAttrs.D9) {
                    myForm.checkItem("industryPatternsAttrsD9");
                }
                if (industryPatternsAttrs.D10) {
                    myForm.checkItem("industryPatternsAttrsD10");
                }
                if (industryPatternsAttrs.D11) {
                    myForm.checkItem("industryPatternsAttrsD11");
                }
                if (industryPatternsAttrs.E1) {
                    myForm.checkItem("industryPatternsAttrsE1");
                }
                if (industryPatternsAttrs.E2) {
                    myForm.checkItem("industryPatternsAttrsE2");
                }
                if (industryPatternsAttrs.E3) {
                    myForm.checkItem("industryPatternsAttrsE3");
                }
                if (industryPatternsAttrs.E4) {
                    myForm.checkItem("industryPatternsAttrsE4");
                }
                if (industryPatternsAttrs.E5) {
                    myForm.checkItem("industryPatternsAttrsE5");
                }
                if (industryPatternsAttrs.E6) {
                    myForm.checkItem("industryPatternsAttrsE6");
                }
                if (industryPatternsAttrs.E7) {
                    myForm.checkItem("industryPatternsAttrsE7");
                }
                if (industryPatternsAttrs.E8) {
                    myForm.checkItem("industryPatternsAttrsE8");
                }
                if (industryPatternsAttrs.E9) {
                    myForm.checkItem("industryPatternsAttrsE9");
                }
                if (industryPatternsAttrs.E10) {
                    myForm.checkItem("industryPatternsAttrsE10");
                }
                if (industryPatternsAttrs.E11) {
                    myForm.checkItem("industryPatternsAttrsE11");
                }
                if (industryPatternsAttrs.F1) {
                    myForm.checkItem("industryPatternsAttrsF1");
                }
                if (industryPatternsAttrs.F2) {
                    myForm.checkItem("industryPatternsAttrsF2");
                }
                if (industryPatternsAttrs.F3) {
                    myForm.checkItem("industryPatternsAttrsF3");
                }
                if (industryPatternsAttrs.F4) {
                    myForm.checkItem("industryPatternsAttrsF4");
                }
                if (industryPatternsAttrs.F5) {
                    myForm.checkItem("industryPatternsAttrsF5");
                }
                if (industryPatternsAttrs.F6) {
                    myForm.checkItem("industryPatternsAttrsF6");
                }
                if (industryPatternsAttrs.F7) {
                    myForm.checkItem("industryPatternsAttrsF7");
                }
                if (industryPatternsAttrs.G1) {
                    myForm.checkItem("industryPatternsAttrsG1");
                }
                if (industryPatternsAttrs.G2) {
                    myForm.checkItem("industryPatternsAttrsG2");
                }
                if (industryPatternsAttrs.G3) {
                    myForm.checkItem("industryPatternsAttrsG3");
                }
                if (industryPatternsAttrs.G4) {
                    myForm.checkItem("industryPatternsAttrsG4");
                }
                if (industryPatternsAttrs.G5) {
                    myForm.checkItem("industryPatternsAttrsG5");
                }
                if (industryPatternsAttrs.G6) {
                    myForm.checkItem("industryPatternsAttrsG6");
                }
                if (industryPatternsAttrs.G7) {
                    myForm.checkItem("industryPatternsAttrsG7");
                }
                if (industryPatternsAttrs.G8) {
                    myForm.checkItem("industryPatternsAttrsG8");
                }
                if (industryPatternsAttrs.G9) {
                    myForm.checkItem("industryPatternsAttrsG9");
                }
                if (industryPatternsAttrs.G10) {
                    myForm.checkItem("industryPatternsAttrsG10");
                }
                if (industryPatternsAttrs.G11) {
                    myForm.checkItem("industryPatternsAttrsG11");
                }
                if (industryPatternsAttrs.G12) {
                    myForm.checkItem("industryPatternsAttrsG12");
                }
                if (industryPatternsAttrs.G13) {
                    myForm.checkItem("industryPatternsAttrsG13");
                }
                if (industryPatternsAttrs.G14) {
                    myForm.checkItem("industryPatternsAttrsG14");
                }
                if (industryPatternsAttrs.G15) {
                    myForm.checkItem("industryPatternsAttrsG15");
                }
                if (industryPatternsAttrs.G16) {
                    myForm.checkItem("industryPatternsAttrsG16");
                }
                if (industryPatternsAttrs.G17) {
                    myForm.checkItem("industryPatternsAttrsG17");
                }
                if (industryPatternsAttrs.H1) {
                    myForm.checkItem("industryPatternsAttrsH1");
                }
                if (industryPatternsAttrs.H2) {
                    myForm.checkItem("industryPatternsAttrsH2");
                }
                if (industryPatternsAttrs.H3) {
                    myForm.checkItem("industryPatternsAttrsH3");
                }
                if (industryPatternsAttrs.H4) {
                    myForm.checkItem("industryPatternsAttrsH4");
                }
                if (industryPatternsAttrs.I1) {
                    myForm.checkItem("industryPatternsAttrsI1");
                }
                if (industryPatternsAttrs.I2) {
                    myForm.checkItem("industryPatternsAttrsI2");
                }
                if (industryPatternsAttrs.I3) {
                    myForm.checkItem("industryPatternsAttrsI3");
                }
                if (industryPatternsAttrs.I4) {
                    myForm.checkItem("industryPatternsAttrsI4");
                }
                if (industryPatternsAttrs.I5) {
                    myForm.checkItem("industryPatternsAttrsI5");
                }
                if (industryPatternsAttrs.I6) {
                    myForm.checkItem("industryPatternsAttrsI6");
                }
                if (industryPatternsAttrs.J1) {
                    myForm.checkItem("industryPatternsAttrsJ1");
                }
                if (industryPatternsAttrs.J2) {
                    myForm.checkItem("industryPatternsAttrsJ2");
                }
                if (industryPatternsAttrs.J3) {
                    myForm.checkItem("industryPatternsAttrsJ3");
                }
                if (industryPatternsAttrs.J4) {
                    myForm.checkItem("industryPatternsAttrsJ4");
                }
                if (industryPatternsAttrs.J5) {
                    myForm.checkItem("industryPatternsAttrsJ5");
                }
                if (industryPatternsAttrs.J6) {
                    myForm.checkItem("industryPatternsAttrsJ6");
                }
                if (industryPatternsAttrs.J7) {
                    myForm.checkItem("industryPatternsAttrsJ7");
                }
                if (industryPatternsAttrs.K1) {
                    myForm.checkItem("industryPatternsAttrsK1");
                }
                if (industryPatternsAttrs.K2) {
                    myForm.checkItem("industryPatternsAttrsK2");
                }
                if (industryPatternsAttrs.K3) {
                    myForm.checkItem("industryPatternsAttrsK3");
                }
                if (industryPatternsAttrs.K4) {
                    myForm.checkItem("industryPatternsAttrsK4");
                }
                if (industryPatternsAttrs.K5) {
                    myForm.checkItem("industryPatternsAttrsK5");
                }
                if (industryPatternsAttrs.K6) {
                    myForm.checkItem("industryPatternsAttrsK6");
                }
                if (industryPatternsAttrs.L1) {
                    myForm.checkItem("industryPatternsAttrsL1");
                }
                if (industryPatternsAttrs.L2) {
                    myForm.checkItem("industryPatternsAttrsL2");
                }
                if (industryPatternsAttrs.L3) {
                    myForm.checkItem("industryPatternsAttrsL3");
                }
                if (industryPatternsAttrs.L4) {
                    myForm.checkItem("industryPatternsAttrsL4");
                }
                if (industryPatternsAttrs.L5) {
                    myForm.checkItem("industryPatternsAttrsL5");
                }
                if (industryPatternsAttrs.L6) {
                    myForm.checkItem("industryPatternsAttrsL6");
                }
                if (industryPatternsAttrs.L7) {
                    myForm.checkItem("industryPatternsAttrsL7");
                }
                if (industryPatternsAttrs.L8) {
                    myForm.checkItem("industryPatternsAttrsL8");
                }
                if (industryPatternsAttrs.L9) {
                    myForm.checkItem("industryPatternsAttrsL9");
                }
                if (industryPatternsAttrs.L10) {
                    myForm.checkItem("industryPatternsAttrsL10");
                }
                if (industryPatternsAttrs.M1) {
                    myForm.checkItem("industryPatternsAttrsM1");
                }
                if (industryPatternsAttrs.M2) {
                    myForm.checkItem("industryPatternsAttrsM2");
                }
                if (industryPatternsAttrs.M3) {
                    myForm.checkItem("industryPatternsAttrsM3");
                }
                if (industryPatternsAttrs.M4) {
                    myForm.checkItem("industryPatternsAttrsM4");
                }
                if (industryPatternsAttrs.M5) {
                    myForm.checkItem("industryPatternsAttrsM5");
                }
                if (industryPatternsAttrs.M6) {
                    myForm.checkItem("industryPatternsAttrsM6");
                }
            }
        },*/
        manageScopeRecover : function(manageScope,manageScopes) {
            var myForm = this.get("myForm");
            if (manageScope != null&&manageScope!="") {
                myForm.setItemValue("manageScope", manageScope);
                manageScope = eval("(" + manageScope + ")");
                if (manageScope.manageScope1) {
                    myForm.checkItem("manageScope1");
                }
                if (manageScope.manageScope2) {
                    myForm.checkItem("manageScope2");
                }
                if (manageScope.manageScope3) {
                    myForm.checkItem("manageScope3");
                }
                if (manageScope.manageScope4) {
                    myForm.checkItem("manageScope4");
                }
                if (manageScope.manageScope5) {
                    myForm.checkItem("manageScope5");
                }
                if (manageScope.manageScope6) {
                    myForm.checkItem("manageScope6");
                }
                if (manageScope.manageScope7) {
                    myForm.checkItem("manageScope7");
                }
                if (manageScope.manageScope8) {
                    myForm.checkItem("manageScope8");
                }
                if (manageScope.manageScope9) {
                    myForm.checkItem("manageScope9");
                }
                if (manageScope.manageScope10) {
                    myForm.checkItem("manageScope10");
                }
                if (manageScope.manageScope11) {
                    myForm.checkItem("manageScope11");
                }
                if (manageScope.manageScope12) {
                    myForm.checkItem("manageScope12");
                }
                if (manageScope.manageScope13) {
                    myForm.checkItem("manageScope13");
                }
                if (manageScope.manageScope14) {
                    myForm.checkItem("manageScope14");
                }
                if (manageScope.manageScope15) {
                    myForm.checkItem("manageScope15");
                }
                if (manageScope.manageScope16) {
                    myForm.checkItem("manageScope16");
                }
                if (manageScope.manageScope17) {
                    myForm.checkItem("manageScope17");
                }
                if (manageScope.manageScope18) {
                    myForm.checkItem("manageScope18");
                }
                if (manageScope.manageScope19) {
                    myForm.checkItem("manageScope19");
                }
                if (manageScope.manageScope20) {
                    myForm.checkItem("manageScope20");
                }
                if (manageScope.manageScope21) {
                    myForm.checkItem("manageScope21");
                }
                if (manageScope.manageScope22) {
                    myForm.checkItem("manageScope22");
                }
                if (manageScope.manageScope23) {
                    myForm.checkItem("manageScope23");
                }
                if (manageScope.manageScope24) {
                    myForm.checkItem("manageScope24");
                }
                if (manageScope.manageScope25) {
                    myForm.checkItem("manageScope25");
                }
                if (manageScope.manageScope26) {
                    myForm.checkItem("manageScope26");
                }
                if (manageScope.manageScope27) {
                    myForm.checkItem("manageScope27");
                }
                if (manageScope.manageScope28) {
                    myForm.checkItem("manageScope28");
                }
                if (manageScope.manageScope29) {
                    myForm.checkItem("manageScope29");
                }
                if (manageScope.manageScope30) {
                    myForm.checkItem("manageScope30");
                }
                if (manageScope.manageScope31) {
                    myForm.checkItem("manageScope31");
                }
                if (manageScope.manageScope32) {
                    myForm.checkItem("manageScope32");
                }
                if (manageScope.manageScope33) {
                    myForm.checkItem("manageScope33");
                }
                if (manageScope.manageScope34) {
                    myForm.checkItem("manageScope34");
                }
                if (manageScope.manageScope35) {
                    myForm.checkItem("manageScope35");
                }
                if (manageScope.manageScope36) {
                    myForm.checkItem("manageScope36");
                }
                if (manageScope.manageScope37) {
                    myForm.checkItem("manageScope37");
                }
                if (manageScope.manageScope38) {
                    myForm.checkItem("manageScope38");
                }
                if (manageScope.manageScope39) {
                    myForm.checkItem("manageScope39");
                }if (manageScope.manageScope40) {
                    myForm.checkItem("manageScope40");
                }
                if (manageScope.manageScope41) {
                    myForm.checkItem("manageScope41");
                }
                if (manageScope.manageScope42) {
                    myForm.checkItem("manageScope42");
                }
                if (manageScope.manageScope43) {
                    myForm.checkItem("manageScope43");
                }
                if (manageScope.manageScope44) {
                    myForm.checkItem("manageScope44");
                }
                if (manageScope.manageScope45) {
                    myForm.checkItem("manageScope45");
                }
                if (manageScope.manageScope46) {
                    myForm.checkItem("manageScope46");
                }
                if (manageScope.manageScope47) {
                    myForm.checkItem("manageScope47");
                }
            }else{
                var _manageScope = _this.get("manageScope");
                _manageScope.manageScope1=false;
                _manageScope.manageScope2=false;
                _manageScope.manageScope3=false;
                _manageScope.manageScope4=false;
                _manageScope.manageScope5=false;
                _manageScope.manageScope6=false;
                _manageScope.manageScope7=false;
                _manageScope.manageScope8=false;
                _manageScope.manageScope9=false;
                _manageScope.manageScope10=false;
                _manageScope.manageScope11=false;
                _manageScope.manageScope12=false;
                _manageScope.manageScope13=false;
                _manageScope.manageScope14=false;
                _manageScope.manageScope15=false;
                _manageScope.manageScope16=false;
                _manageScope.manageScope17=false;
                _manageScope.manageScope18=false;
                _manageScope.manageScope19=false;
                _manageScope.manageScope20=false;
                _manageScope.manageScope21=false;
                _manageScope.manageScope22=false;
                _manageScope.manageScope23=false;
                _manageScope.manageScope24=false;
                _manageScope.manageScope25=false;
                _manageScope.manageScope26=false;
                _manageScope.manageScope27=false;
                _manageScope.manageScope28=false;
                _manageScope.manageScope29=false;
                _manageScope.manageScope30=false;
                _manageScope.manageScope31=false;
                _manageScope.manageScope32=false;
                _manageScope.manageScope33=false;
                _manageScope.manageScope34=false;
                _manageScope.manageScope35=false;
                _manageScope.manageScope36=false;
                _manageScope.manageScope37=false;
                _manageScope.manageScope38=false;
                _manageScope.manageScope39=false;
                _manageScope.manageScope40=false;
                _manageScope.manageScope41=false;
                _manageScope.manageScope42=false;
                _manageScope.manageScope43=false;
                _manageScope.manageScope44=false;
                _manageScope.manageScope45=false;
                _manageScope.manageScope46=false;
                _manageScope.manageScope47=false;
                myForm.setItemValue("manageScope", _manageScope);
            }
        }
    });
    return CityNodeDel;
});