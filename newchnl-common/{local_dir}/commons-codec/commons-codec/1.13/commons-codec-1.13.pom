<?xml version="1.0"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one or more
contributor license agreements.  See the NOTICE file distributed with
this work for additional information regarding copyright ownership.
The ASF licenses this file to You under the Apache License, Version 2.0
(the "License"); you may not use this file except in compliance with
the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<!--
 To produce reports, use the reporting profile, for example: mvn -Preporting clean site
 You may need to use the -U option to update your environment if you get an error.
 -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>48</version>
  </parent>
  <groupId>commons-codec</groupId>
  <artifactId>commons-codec</artifactId>
  <!-- Remember to update the version in default.properties as well -->
  <version>1.13</version>
  <name>Apache Commons Codec</name>
  <inceptionYear>2002</inceptionYear>
  <description>
     The Apache Commons Codec package contains simple encoder and decoders for
     various formats such as Base64 and Hexadecimal.  In addition to these
     widely used encoders and decoders, the codec package also maintains a
     collection of phonetic encoding utilities.
  </description>
  <url>https://commons.apache.org/proper/commons-codec/</url>
  <issueManagement>
    <system>jira</system>
    <url>https://issues.apache.org/jira/browse/CODEC</url>
  </issueManagement>
  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf?p=commons-codec.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf?p=commons-codec.git</developerConnection>
    <url>https://github.com/apache/commons-codec</url>
  </scm>
  <distributionManagement>
    <site>
      <id>stagingSite</id>
      <name>Apache Staging Website</name>
      <url>${commons.deployment.protocol}://people.apache.org/www/commons.apache.org/${commons.componentid}/</url>
    </site>
  </distributionManagement>
  <developers>
    <developer>
      <name>Henri Yandell</name>
      <id>bayard</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Tim OBrien</name>
      <id>tobrien</id>
      <email><EMAIL></email>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <name>Scott Sanders</name>
      <id>sanders</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Rodney Waldhoff</name>
      <id>rwaldhoff</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Daniel Rall</name>
      <id>dlr</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Jon S. Stevens</name>
      <id>jon</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Gary Gregory</name>
      <id>ggregory</id>
      <email><EMAIL></email>
      <url>http://www.garygregory.com</url>
      <timezone>-5</timezone>
    </developer>
    <developer>
      <name>David Graham</name>
      <id>dgraham</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Julius Davies</name>
      <id>julius</id>
      <email><EMAIL></email>
      <organizationUrl>http://juliusdavies.ca/</organizationUrl>
      <timezone>-8</timezone>
    </developer>
    <developer>
      <name>Thomas Neidhart</name>
      <id>tn</id>
      <email><EMAIL></email>
    </developer>
    <developer>
      <name>Rob Tompkins</name>
      <id>chtompki</id>
      <email><EMAIL></email>
    </developer>
  </developers>
  <contributors>
    <contributor>
      <name>Christopher O'Brien</name>
      <email><EMAIL></email>
      <roles>
        <role>hex</role>
        <role>md5</role>
        <role>architecture</role>
      </roles>
    </contributor>
    <contributor>
      <name>Martin Redington</name>
      <roles>
        <role>Representing xml-rpc</role>
      </roles>
    </contributor>
    <contributor>
      <name>Jeffery Dever</name>
      <roles>
        <role>Representing http-client</role>
      </roles>
    </contributor>
    <contributor>
      <name>Steve Zimmermann</name>
      <email><EMAIL></email>
      <roles>
        <role>Documentation</role>
      </roles>
    </contributor>
    <contributor>
      <name>Benjamin Walstrum</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Oleg Kalnichevski</name>
      <email><EMAIL></email>
      <roles>
        <role>Representing http-client</role>
      </roles>
    </contributor>
    <contributor>
      <name>Dave Dribin</name>
      <email><EMAIL></email>
      <roles>
        <role>DigestUtil</role>
      </roles>
    </contributor>
    <contributor>
      <name>Alex Karasulu</name>
      <email>aok123 at bellsouth.net</email>
      <roles>
        <role>Submitted Binary class and test</role>
      </roles>
    </contributor>
    <contributor>
      <name>Matthew Inger</name>
      <email>mattinger at yahoo.com</email>
      <roles>
        <role>Submitted DIFFERENCE algorithm for Soundex and RefinedSoundex</role>
      </roles>
    </contributor>
    <contributor>
      <name>Jochen Wiedmann</name>
      <email><EMAIL></email>
      <roles>
        <role>Base64 code [CODEC-69]</role>
      </roles>
    </contributor>
    <contributor>
      <name>Sebastian Bazley</name>
      <email><EMAIL></email>
      <roles>
        <role>Streaming Base64</role>
      </roles>
    </contributor>
    <contributor>
      <name>Matthew Pocock</name>
      <email><EMAIL></email>
      <roles>
        <role>Beider-Morse phonetic matching</role>
      </roles>
    </contributor>
    <contributor>
      <name>Colm Rice</name>
      <email>colm_rice at hotmail dot com</email>
      <roles>
        <role>Submitted Match Rating Approach (MRA) phonetic encoder and tests [CODEC-161]</role>
      </roles>
    </contributor>
  </contributors>
  <!-- Codec only has test dependencies ATM -->
  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <!-- 3.9+ needs Java8 -->
      <version>3.8</version>
      <scope>test</scope>
    </dependency>
  </dependencies>
  <properties>
    <maven.compiler.source>1.7</maven.compiler.source>
    <maven.compiler.target>1.7</maven.compiler.target>
    <!-- Fix to build on JDK 7: version 4.0.0 requires Java 8. -->
    <!-- Can be dropped when CP 49 is released -->
    <commons.felix.version>3.5.1</commons.felix.version>
    <commons.componentid>codec</commons.componentid>
    <commons.module.name>org.apache.commons.codec</commons.module.name>
    <commons.jira.id>CODEC</commons.jira.id>
    <commons.jira.pid>12310464</commons.jira.pid>
    <!-- Ensure copies work OK (can be removed later when this is in parent POM) -->
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <commons.encoding>UTF-8</commons.encoding>
    <checkstyle.header.file>${basedir}/LICENSE-header.txt</checkstyle.header.file>
    <checkstyle.version>2.17</checkstyle.version>
    <commons.japicmp.version>0.14.1</commons.japicmp.version>
    <commons.jacoco.version>0.8.4</commons.jacoco.version>

    <!-- generate report even if there are binary incompatible changes -->
    <commons.japicmp.breakBuildOnBinaryIncompatibleModifications>false</commons.japicmp.breakBuildOnBinaryIncompatibleModifications>
    <japicmp.skip>false</japicmp.skip>

    <!-- Commons Release Plugin -->
    <commons.bc.version>1.12</commons.bc.version>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.release-plugin.version>1.5</commons.release-plugin.version>
    <commons.release.isDistModule>true</commons.release.isDistModule>
    <commons.distSvnStagingUrl>scm:svn:https://dist.apache.org/repos/dist/dev/commons/${commons.componentid}</commons.distSvnStagingUrl>
    <commons.releaseManagerName>Gary Gregory</commons.releaseManagerName>    
    <commons.releaseManagerKey>86fdc7e2a11262cb</commons.releaseManagerKey>
  </properties>
  <build>
    <defaultGoal>clean verify apache-rat:check clirr:check javadoc:javadoc</defaultGoal>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>${commons.scm-publish.version}</version>
          <configuration>
            <ignorePathsToDelete>
              <ignorePathToDelete>archive**</ignorePathToDelete>
            </ignorePathsToDelete>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>

      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>src/test/resources/bla.tar.xz</exclude>
          </excludes>
        </configuration>
      </plugin>

      <!-- Add Java 9 Automatic-Module-Name -->
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive combine.children="append">
            <!-- Temporary fix, remove this after this has implemented in parent pom -->
            <manifestEntries>
              <Automatic-Module-Name>${commons.module.name}</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <excludes>
            <exclude>**/*AbstractTest.java</exclude>
            <exclude>**/*PerformanceTest.java</exclude>
          </excludes>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>src/assembly/bin.xml</descriptor>
            <descriptor>src/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <!-- Allow use of mvn checkstyle:checkstyle. Must agree with reporting section below. -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.version}</version>
        <configuration>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <headerFile>${basedir}/LICENSE-header.txt</headerFile>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>${checkstyle.version}</version>
        <configuration>
          <configLocation>${basedir}/checkstyle.xml</configLocation>
          <enableRulesSummary>false</enableRulesSummary>
          <headerFile>${basedir}/LICENSE-header.txt</headerFile>
        </configuration>
        <!-- We need to specify reportSets because 2.9.1 creates two reports -->
        <reportSets>
          <reportSet>
            <reports>
              <report>checkstyle</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>3.12.0</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
          <linkXref>true</linkXref>
          <rulesets>
            <ruleset>${basedir}/pmd.xml</ruleset>
          </rulesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>findbugs-maven-plugin</artifactId>
	    <version>${commons.findbugs.version}</version>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>taglist-maven-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <tags>
            <tag>TODO</tag>
            <tag>NOPMD</tag>
            <tag>NOTE</tag>
          </tags>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>javancss-maven-plugin</artifactId>
        <version>2.1</version>
      </plugin>
    </plugins>
  </reporting>
  <profiles>
    <profile>
      <id>java9+</id>
      <activation>
        <jdk>[9,)</jdk>
      </activation>
      <properties>
        <!-- coverall version 4.3.0 does not work with java 9, see https://github.com/trautonen/coveralls-maven-plugin/issues/112 -->
        <coveralls.skip>true</coveralls.skip>
      </properties>
    </profile>
  </profiles>
</project>
