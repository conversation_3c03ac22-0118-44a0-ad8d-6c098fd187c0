<?xml version="1.0"?>
<!--
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->
<project
    xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>11</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>commons-beanutils</groupId>
  <artifactId>commons-beanutils</artifactId>
  <version>1.8.0</version>
  <name>Commons BeanUtils</name>

  <inceptionYear>2000</inceptionYear>
  <description>BeanUtils provides an easy-to-use but flexible wrapper around reflection and introspection.</description>

  <url>http://commons.apache.org/beanutils/</url>

  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/BEANUTILS</url>
  </issueManagement>

  <scm>
    <connection>scm:svn:http://svn.apache.org/repos/asf/commons/proper/beanutils/trunk/</connection>
    <developerConnection>scm:svn:https://svn.apache.org/repos/asf/commons/proper/beanutils/trunk/</developerConnection>
    <url>http://svn.apache.org/viewvc/commons/proper/beanutils/trunk/</url>
  </scm>

  <developers>
    <developer>
      <name>Robert Burrell Donkin</name>
      <id>rdonkin</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>dIon Gillard</name>
      <id>dion</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Craig McClanahan</name>
      <id>craigmcc</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Geir Magnusson Jr.</name>
      <id>geirm</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Scott Sanders</name>
      <id>sanders</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>James Strachan</name>
      <id>jstrachan</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Rodney Waldhoff</name>
      <id>rwaldhoff</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Martin van den Bemt</name>
      <id>mvdb</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Yoav Shapira</name>
      <id>yoavs</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Niall Pemberton</name>
      <id>niallp</id>
      <email>niallp at apache dot org</email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>Simon Kitching</name>
      <id>skitching</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
    <developer>
      <name>James Carman</name>
      <id>jcarman</id>
      <email><EMAIL></email>
      <organization>Apache Software Foundation</organization>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Paul Jack</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Stephen Colebourne</name>
      <email></email>
    </contributor>
    <contributor>
      <name>Berin Loritsch</name>
      <email></email>
    </contributor>    
  </contributors>

  <dependencies>
    <dependency>
      <groupId>commons-logging</groupId>
      <artifactId>commons-logging</artifactId>
      <version>1.1.1</version>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId> 
      <version>3.2.1</version> 
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections-testframework</artifactId> 
      <version>3.2.1</version> 
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>3.8.1</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
      <sourceDirectory>src/java</sourceDirectory>
      <testSourceDirectory>src/test</testSourceDirectory>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
            <configuration>
              <forkMode>pertest</forkMode>
              <!-- limit memory size see BEANUTILS-291 -->
              <argLine>-Xmx25M</argLine>
              <includes>
                <include>**/*TestCase.java</include>
              </includes>
              <excludes>
                <!-- This test case is known to fail, and there isn't any proposed fix
                  -  so we will just exclude it until someone comes up with a solution.
                -->
                <exclude>**/*MemoryTestCase.java</exclude>
              </excludes>

              <!-- Configure Logging -->
              <systemProperties>
                  <property>
                      <name>org.apache.commons.logging.LogFactory</name>
                      <value>org.apache.commons.logging.impl.LogFactoryImpl</value>
                  </property>
                  <property>
                      <name>org.apache.commons.logging.Log</name>
                      <value>org.apache.commons.logging.impl.SimpleLog</value>
                  </property>
                  <property>
                      <name>org.apache.commons.logging.simplelog.defaultlog</name>
                      <value>WARN</value>
                  </property>
              </systemProperties>

          </configuration>
        </plugin>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <executions>
            <execution>
              <phase>package</phase>
              <configuration>
                <tasks>
                  <ant antfile="build-other-jars.xml" target="other-jars">
                    <property name="component.version"    value="${project.version}"/>
                    <property name="build.home"           value="${project.build.directory}"/>
                    <property name="dist.home"            value="${project.build.directory}"/>
                    <property name="maven.compile.source" value="${maven.compile.source}"/>
                    <property name="maven.compile.target" value="${maven.compile.target}"/>
                  </ant>
                </tasks>
              </configuration>
              <goals>
                <goal>run</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
            <artifactId>maven-assembly-plugin</artifactId>
            <configuration>
                <descriptors>
                    <descriptor>src/main/assembly/bin.xml</descriptor>
                    <descriptor>src/main/assembly/src.xml</descriptor>
                </descriptors>
                <tarLongFileMode>gnu</tarLongFileMode>
            </configuration>
        </plugin>
      </plugins>
    </build>

    <properties>
        <maven.compile.source>1.3</maven.compile.source>
        <maven.compile.target>1.3</maven.compile.target>
        <commons.componentid>beanutils</commons.componentid>
        <commons.release.version>1.8.0</commons.release.version>
        <commons.jira.id>BEANUTILS</commons.jira.id>
        <commons.jira.pid>12310460</commons.jira.pid>
        <commons.osgi.export>
             !org.apache.commons.collections,
              org.apache.commons.beanutils.*;version=${pom.version}
        </commons.osgi.export>
    </properties> 

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>2.1</version>
                <configuration>
                    <configLocation>${basedir}/checkstyle.xml</configLocation>
                    <enableRulesSummary>false</enableRulesSummary>
                    <headerFile>${basedir}/license-header.txt</headerFile>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>clirr-maven-plugin</artifactId>
                <version>2.2.2</version>
                <configuration>
                    <comparisonVersion>1.8.0-BETA</comparisonVersion>
                    <minSeverity>info</minSeverity>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.4</version>
                <configuration>
                    <linksource>true</linksource>
                    <links>
                        <link>http://java.sun.com/j2se/1.4.2/docs/api/</link>
                        <link>http://commons.apache.org/collections/api-release/</link>
                    </links>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

</project>
