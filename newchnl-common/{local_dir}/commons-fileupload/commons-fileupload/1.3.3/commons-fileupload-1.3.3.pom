<?xml version="1.0" encoding="UTF-8"?>
<!--
 Licensed to the Apache Software Foundation (ASF) under one or more
 contributor license agreements.  See the NOTICE file distributed with
 this work for additional information regarding copyright ownership.
 The ASF licenses this file to You under the Apache License, Version 2.0
 (the "License"); you may not use this file except in compliance with
 the License.  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-parent</artifactId>
    <version>41</version>
  </parent>

  <groupId>commons-fileupload</groupId>
  <artifactId>commons-fileupload</artifactId>
  <version>1.3.3</version>

  <name>Apache Commons FileUpload</name>
  <description>
    The Apache Commons FileUpload component provides a simple yet flexible means of adding support for multipart
    file upload functionality to servlets and web applications.
  </description>
  <url>http://commons.apache.org/proper/commons-fileupload/</url>
  <inceptionYear>2002</inceptionYear>

  <developers>
    <developer>
      <name>Martin Cooper</name>
      <id>martinc</id>
      <email><EMAIL></email>
      <organization>Yahoo!</organization>
    </developer>
    <developer>
      <name>dIon Gillard</name>
      <id>dion</id>
      <email><EMAIL></email>
      <organization>Multitask Consulting</organization>
    </developer>
    <developer>
      <name>John McNally</name>
      <id>jmcnally</id>
      <email><EMAIL></email>
      <organization>CollabNet</organization>
    </developer>
    <developer>
      <name>Daniel Rall</name>
      <id>dlr</id>
      <email><EMAIL></email>
      <organization>CollabNet</organization>
    </developer>
    <developer>
      <name>Jason van Zyl</name>
      <id>jvanzyl</id>
      <email><EMAIL></email>
      <organization>Zenplex</organization>
    </developer>
    <developer>
      <name>Robert Burrell Donkin</name>
      <id>rdonkin</id>
      <email><EMAIL></email>
      <organization />
    </developer>
    <developer>
      <name>Sean C. Sullivan</name>
      <id>sullis</id>
      <email>sean |at| seansullivan |dot| com</email>
      <organization />
    </developer>
    <developer>
      <name>Jochen Wiedmann</name>
      <id>jochen</id>
      <email><EMAIL></email>
      <organization />
    </developer>
    <developer>
      <name>Simone Tripodi</name>
      <id>simonetripodi</id>
      <email><EMAIL></email>
      <organization>Adobe</organization>
    </developer>
    <developer>
      <name>Gary Gregory</name>
      <id>ggregory</id>
      <email><EMAIL></email>
      <organization />
    </developer>
    <developer>
      <name>Rob Tompkins</name>
      <id>chtompki</id>
      <email><EMAIL></email>
      <organization />
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Aaron Freeman</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Daniel Fabian</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Jörg Heinicke</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Stepan Koltsov</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Michael Macaluso</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Amichai Rothman</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Alexander Sova</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Paul Spurr</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Thomas Vandahl</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Henry Yandell</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Jan Novotný</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>frank</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Rafal Krzewski</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Sean Legassick</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>Oleg Kalnichevski</name>
      <email><EMAIL></email>
    </contributor>
    <contributor>
      <name>David Sean Taylor</name>
      <email><EMAIL></email>
    </contributor>
  </contributors>

  <scm>
    <connection>scm:git:http://git-wip-us.apache.org/repos/asf/commons-fileupload.git</connection>
    <developerConnection>scm:git:https://git-wip-us.apache.org/repos/asf/commons-fileupload.git</developerConnection>
    <url>https://git-wip-us.apache.org/repos/asf?p=commons-fileupload.git</url>
    <tag>commons-fileupload-1.3.3-RC6</tag>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>http://issues.apache.org/jira/browse/FILEUPLOAD</url>
  </issueManagement>

  <properties>
    <maven.compiler.source>1.5</maven.compiler.source>
    <maven.compiler.target>1.5</maven.compiler.target>
    <maven.compile.encoding>ISO-8859-1</maven.compile.encoding>
    <commons.componentid>fileupload</commons.componentid>
    <commons.release.version>1.3.3</commons.release.version>
    <commons.rc.version>RC1</commons.rc.version>
    <commons.jira.id>FILEUPLOAD</commons.jira.id>
    <commons.jira.pid>12310476</commons.jira.pid>
    <commons.osgi.export>!org.apache.commons.fileupload.util.mime,org.apache.commons.*;version=${project.version};-noimport:=true</commons.osgi.export>
    <commons.osgi.import>!javax.portlet,*</commons.osgi.import>
    <commons.osgi.dynamicImport>javax.portlet</commons.osgi.dynamicImport>
    <project.scm.id>git-wip-us.apache.org</project.scm.id>
  </properties>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>servlet-api</artifactId>
      <version>2.4</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>portlet-api</groupId>
      <artifactId>portlet-api</artifactId>
      <version>1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.2</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <descriptors>
            <descriptor>${basedir}/src/main/assembly/bin.xml</descriptor>
            <descriptor>${basedir}/src/main/assembly/src.xml</descriptor>
          </descriptors>
          <tarLongFileMode>gnu</tarLongFileMode>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-release-plugin</artifactId>
        <configuration>
          <preparationGoals>clean site verify</preparationGoals>
          <goals>clean site deploy</goals>
        </configuration>
      </plugin>
    </plugins>
    <pluginManagement>
    	<plugins>
    	    <plugin>
    	      <groupId>org.apache.maven.plugins</groupId>
    	      <artifactId>maven-release-plugin</artifactId>
    	      <configuration>
    	        <tagBase>https://svn.apache.org/repos/asf/commons/proper/fileupload/tags</tagBase>
    	      </configuration>
    	    </plugin>
    		<!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
    		<plugin>
    			<groupId>org.eclipse.m2e</groupId>
    			<artifactId>lifecycle-mapping</artifactId>
    			<version>1.0.0</version>
    			<configuration>
    				<lifecycleMappingMetadata>
    					<pluginExecutions>
    						<pluginExecution>
    							<pluginExecutionFilter>
    								<groupId>
    									org.apache.maven.plugins
    								</groupId>
    								<artifactId>
    									maven-antrun-plugin
    								</artifactId>
    								<versionRange>[1.7,)</versionRange>
    								<goals>
    									<goal>run</goal>
    								</goals>
    							</pluginExecutionFilter>
    							<action>
    								<ignore />
    							</action>
    						</pluginExecution>
    					</pluginExecutions>
    				</lifecycleMappingMetadata>
    			</configuration>
    		</plugin>
    	</plugins>
    </pluginManagement>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-changes-plugin</artifactId>
        <version>${commons.changes.version}</version>
        <configuration>
          <issueLinkTemplate>%URL%/../%ISSUE%</issueLinkTemplate>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>changes-report</report>
              <!--  NPE, retry from time to time <report>jira-report</report>  -->
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>2.10</version>
        <configuration>
          <configLocation>${basedir}/src/checkstyle/fileupload_checks.xml</configLocation>
          <suppressionsLocation>${basedir}/src/checkstyle/checkstyle-suppressions.xml</suppressionsLocation>
          <enableRulesSummary>false</enableRulesSummary>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-pmd-plugin</artifactId>
        <version>2.7.1</version>
        <configuration>
          <targetJdk>${maven.compiler.target}</targetJdk>
          <rulesets>
            <ruleset>${basedir}/src/checkstyle/fileupload_basic.xml</ruleset>
          </rulesets>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <version>${commons.clirr.version}</version>
        <configuration>
          <comparisonVersion>1.3</comparisonVersion>
        </configuration>
      </plugin>
    </plugins>
  </reporting>
</project>
