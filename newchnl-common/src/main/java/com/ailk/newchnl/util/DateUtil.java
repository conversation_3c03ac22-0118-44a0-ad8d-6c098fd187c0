/*
 * $Id: DateUtil.java,v 1.20 2015/01/19 16:44:20 wangjin Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @version $Id: DateUtil.java,v 1.20 2015/01/19 16:44:20 wangjin Exp $ Created
 *          on 2014年7月31日 下午1:01:06 这个公共类主要是用于时间格式转换
 */
public class DateUtil {

	/**
	 * <AUTHOR> 将 String 转成 DATE
	 * @param strFormat
	 *            日期格式
	 * @param strDate
	 * @return
	 */
	public static Date getDateFromStr(String strFormat, String strDate) {
		Date date;
		SimpleDateFormat sdf = new SimpleDateFormat(strFormat);
		try {
			date = sdf.parse(strDate);
		} catch (Exception e) {
			date = new Date(0);
		}
		return date;
	}

	/**
	 * <AUTHOR> 按指定格式,格式化当前时间和日期
	 * @param patter
	 *            制定日期时间显示格式，如‘yyyy/MM/dd HH:mm:ss’
	 * @return String 返回格式化后的日期
	 * **/
	public static String formatDateTime(String patter) {
		java.util.Date d = new java.util.Date();
		java.text.SimpleDateFormat format = new java.text.SimpleDateFormat(patter);
		return format.format(d);
	}

	/***
	 * @date 2014/07/31
	 * @param 转换日期格式
	 *            MM/d/yyyy -->yyyy/MM/d
	 * @param dateStr
	 * @return
	 */
	public static Date getDateUtil(String dateStr) {
		try {
			if (!dateStr.equals("")) {
				SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy");
				Date date = format.parse(dateStr);// 有异常要捕获
				format = new SimpleDateFormat("yyyy/MM/dd");
				String str = format.format(date);
				Date dates = format.parse(str);
				return dates;
			} else {
				return null;
			}
		} catch (Exception e) {
			return null;
		}
	}

	/***
	 * @date 2014/07/31
	 * @param dateStr
	 *            String Date
	 * @param strformat
	 *            String 返回日期格式 (传进来是什么格式返回的就是什么格式)
	 * @return
	 */
	public static Date getDateUtil(String dateStr, String strformat) {
		Date date = null;
		try {
			SimpleDateFormat format = new SimpleDateFormat(strformat);
			if (dateStr != null && !("").equals(dateStr)) {
				date = format.parse(dateStr);// 有异常要捕获
			} else {
				date = new Date();
				String strDate = format.format(date);
				date = format.parse(strDate);
			}
		} catch (Exception e) {
			return null;
		}
		return date;
	}

	/**
	 * 格式化当前时间
	 * 
	 * @param pattern
	 * @return
	 */
	public static String formatCurrentDate(String pattern) {
		DateFormat df = new SimpleDateFormat(pattern);
		return df.format(getCurrDate());
	}

	/**
	 * 格式化失效时间
	 * 
	 * @param pattern
	 * @return
	 */
	public static String formatExpireDate(String pattern) {
		DateFormat df = new SimpleDateFormat(pattern);
		return df.format(getExpireDate());
	}

	/**
	 * 获取当前时间
	 * 
	 * @return
	 */
	public static Date getCurrDate() {
		return new Date();
	}

	/**
	 * 获取失效时间为：2099/12/31
	 * 
	 * @return
	 */
	public static Date getExpireDate() {
		Calendar cal = Calendar.getInstance();
		cal.set(2099, 11, 31);
		return cal.getTime();
	}

	/**
	 * 获取本月一号
	 * 
	 * @return
	 */
	public static Date getThisMonthNumberOne() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime();
	}

	/**
	 * 获取下个月一号
	 * 
	 * @return
	 */
	public static Date getNextMonthNumberOne() {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.add(Calendar.MONTH, 1);
		return calendar.getTime();
	}

	/**
	 * 获取下个月一号 yyyy-MM-dd
	 * 
	 * @return
	 */
	public static Date getNextMonthNumberOneYMD() throws Exception {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_MONTH, 1);
		calendar.add(Calendar.MONTH, 1);
		String dateTime = sdf.format(calendar.getTime());
		return sdf.parse(dateTime);
	}

	/**
	 * 将Date转成String
	 */
	public static String getExpireDate(Date date) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(date);
	}

	/**
	 * 根据当前时间后几个月日期
	 * 
	 * @param iNextMonthNum
	 *            后几个月
	 * @return 返回后几个月日期
	 */
	public static Date getNextMonthDate(int iNextMonthNum) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.DAY_OF_MONTH, iNextMonthNum);
		calendar.add(Calendar.MONTH, iNextMonthNum);
		return calendar.getTime();
	}

	/**
	 * 根据当前时间后几个月日期
	 * 
	 * @param baseDate
	 *            传入日期
	 * @param iNextMonthNum
	 *            后几个月
	 * @return 返回传入时间的后几个月日期
	 */
	public static Date getNextMonthDate(Date baseDate, int iNextMonthNum) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(baseDate);
		calendar.set(Calendar.DAY_OF_MONTH, iNextMonthNum);
		calendar.add(Calendar.MONTH, iNextMonthNum);
		return calendar.getTime();
	}

	/**
	 * 根据当前时间后几个月日期
	 *
	 * @param baseDate
	 *            传入日期
	 * @param iNextMonthNum
	 *            后几个月
	 * @return 返回传入时间的后几个月日期
	 */
	public static Date getNextMonth(Date baseDate, int iNextMonthNum) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(baseDate);
		calendar.add(Calendar.MONTH, iNextMonthNum);
		return calendar.getTime();
	}
	/**
	 * 
	 */
	public static Date getNextDayDate(Date baseDate, int iNextMonthNum) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(baseDate);
		calendar.add(Calendar.DATE, iNextMonthNum);
		return calendar.getTime();
	}

	public static String getCurrentDateFormat() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		return sdf.format(getCurrDate());
	}

	public static String formatDate(Date date, String format) {
		if (date == null) {
			throw new NullPointerException("格式化的日期不能为空！");
		}
		String defaultFormat = "yyyy-MM-dd";
		if (StringUtils.isNotBlank(format)) {
			defaultFormat = format;
		}
		SimpleDateFormat sdf = new SimpleDateFormat(defaultFormat);
		return sdf.format(date);
	}

	/**
	 * 
	 * @param iYear
	 *            年度
	 * @param iMonth
	 *            月份
	 * @param iDay
	 *            日
	 * @return 返回该年月日
	 */
	public static Calendar formatDate(int iYear, int iMonth, int iDay) {
		Calendar cal = Calendar.getInstance();
		cal.set(iYear, iMonth, iDay);
		return cal;
	}

	/**
	 * 加减某些天数
	 * 
	 * @param date
	 * @param amount
	 * @return
	 */
	public static Date addDate(Date date, int amount) {
		if (date == null) {
			throw new NullPointerException();
		}
		Calendar calendar = new GregorianCalendar();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_MONTH, 1);
		return calendar.getTime();
	}

	// public static void main(String[] args) throws ParseException {
	// SimpleDateFormat format = new SimpleDateFormat("MM/dd/yyyy");
	//
	// System.out.println(format.parse("2014/12/28"));
	// Date a = DateUtil.getNextDayDate(DateUtil.getDateUtil("2014/12/28"), 7);
	// System.out.println(DateUtil.formatDate(a, "yyyy-MM-dd"));
	// System.out.println(a.getMonth());
	// }

	/**
	 * 计算以但前时间为基准的之前或之后年分的时间
	 * @param iaddYear 添加多少年
	 * @param iMonth 月份
	 * @param iDay 月份中的某一天
	 * @return
	 */
	public static Date getDate(int iaddYear,int iMonth,int iDay){
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.YEAR,iaddYear);
		cal.set(Calendar.MONTH,iMonth);
		cal.set(Calendar.DAY_OF_MONTH,iDay);
		return cal.getTime();
	}

	/**
	 * 获取时间的差异
	 * @param date1 开始时间
	 * @param date2 结束时间
	 * @return
	 */
	public static int getMonthDifference(Date date1, Date date2) {
		Calendar cal1 = Calendar.getInstance();
		Calendar cal2 = Calendar.getInstance();
		cal1.setTime(date1);
		cal2.setTime(date2);
		int year1 = cal1.get(Calendar.YEAR);
		int month1 = cal1.get(Calendar.MONTH);
		int year2 = cal2.get(Calendar.YEAR);
		int month2 = cal2.get(Calendar.MONTH);
		int day1 = cal1.get(Calendar.DAY_OF_MONTH);
		int day2 = cal2.get(Calendar.DAY_OF_MONTH);
		if (day2>day1){
			//如果 day2里的日 大于 day1 ，则顺延一月
			return (year2 - year1) * 12 + (month2 - month1) + 1;
		}
		return (year2 - year1) * 12 + (month2 - month1);
	}

}
